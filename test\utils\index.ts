import { INestApplication } from '@nestjs/common';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { Test } from '@nestjs/testing';
import { getDataSourceToken, TypeOrmModule } from '@nestjs/typeorm';
import { AgeGateDTO } from '@superawesome/freekws-agegate-api-common';
import { EncryptionService, HashingService } from '@superawesome/freekws-auth-library';
import { WebhookDTO } from '@superawesome/freekws-callback-service-common';
import { EOAuthScope } from '@superawesome/freekws-classic-wrapper-common';
import { SALogger } from '@superawesome/freekws-common-logger';
import { ServiceWebhookListDTO } from '@superawesome/freekws-devportal-common/types/service-webhook/service-webhook.dto';
import { FamilyGroupListDTO } from '@superawesome/freekws-family-service-common';
import {
  ConfirmedGuardianLinkDTO,
  GuardianLinkListDTO,
} from '@superawesome/freekws-family-service-common/types/family-group/internal-admin-family-group.dto';
import { EWebhookName } from '@superawesome/freekws-queue-messages/webhook/webhook.dto';
import { TServiceID } from '@superawesome/freekws-service-activation-service-common';
import {
  ESettingBooleanOrder,
  ESettingConsentType,
  ESettingValueType,
  EUserSettingValueEffectiveSource,
} from '@superawesome/freekws-settings-common';
import { UserSettingValueShortDTO } from '@superawesome/freekws-settings-common/types/user-setting-value/user-setting-value.dto';
import { KeycloakTestUtils, TTokenResponse } from '@superawesome/freekws-test-keycloak';
import { AxiosError } from 'axios';
import { randomUUID } from 'crypto';
import { differenceInYears } from 'date-fns';
import nock from 'nock';
import { createHmac } from 'node:crypto';
import * as QueryString from 'querystring';
import request, { Response } from 'supertest';
import { DataSource, Repository } from 'typeorm';
import { Builder, fixturesIterator, IEntity, Loader, Parser, Resolver } from 'typeorm-fixtures-cli';

import { ageGateResponse } from './constants';
import { makeRequest } from './request-helper';
import { TestConfigService } from './test-config.service';
import { AppTranslation } from '../../src/app/app-translation.entity';
import { App } from '../../src/app/app.entity';
import { bootstrap } from '../../src/bootstrap';
import { ConfigModule } from '../../src/common/services/config/config.module';
import { ConfigService } from '../../src/common/services/config/config.service';
import { JWKService } from '../../src/oauth/jwk.service';
import { JwtUserPayload } from '../../src/oauth/types';
import { RootAppModule } from '../../src/root-app.module';
import { Activation } from '../../src/user/activation.entity';
import { User } from '../../src/user/user.entity';
import { OrgLevelWebhookPayload } from '../../src/webhook/types';

export type TFixture = Record<string, IEntity[]>;

export const voiceSetting = {
  namespace: 'chat',
  settingName: 'voice',
  preferredValue: true,
  preferredValueFromOrgLevel: false,
  effectiveValue: true,
  isOrgLevel: false,
  effectiveSource: EUserSettingValueEffectiveSource.DEFAULT,
  consentRequestedAt: 178949839,
  definition: {
    ageBracket: {
      consentType: ESettingConsentType.OPT_OUT,
    },
    orgId: '6c039d20-8a03-45de-ba5e-54b3ef581d85',
    namespace: 'chat',
    settingName: 'voice',
    valueType: ESettingValueType.BOOLEAN,
    translations: {
      en: {
        label: 'Voice chat',
        parentNotice: 'Allow user to use voice chat',
      },
    },
    restrictiveOrder: ESettingBooleanOrder.FALSE_RESTRICTIVE_TRUE_PERMISSIVE,
    userHidden: false,
    userReadOnly: false,
    required: false,
  },
} as UserSettingValueShortDTO;

// eslint-disable-next-line unicorn/no-static-only-class
export class Utils {
  static app: NestFastifyApplication;
  static configService: ConfigService;
  static encryptionService: EncryptionService;
  static hashingService = new HashingService(32, 10000, '');
  static fixtures: TFixture;
  static appRepo: Repository<App>;
  static appTranslationRepo: Repository<AppTranslation>;
  static userRepo: Repository<User>;
  static activationRepo: Repository<Activation>;
  static logger: SALogger;
  static keycloakTestUtils: KeycloakTestUtils;

  static async createTestServer(): Promise<NestFastifyApplication> {
    const testModule = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          inject: [ConfigService],
          useFactory: (config: ConfigService) => config.getTypeormConfig(),
        }),
        RootAppModule,
      ],
    })
      .overrideProvider(ConfigService)
      .useClass(TestConfigService)
      .compile();

    const app = testModule.createNestApplication<NestFastifyApplication>(new FastifyAdapter());

    Utils.configService = app.get<ConfigService>(ConfigService);
    Utils.encryptionService = app.get<EncryptionService>(EncryptionService);

    const dataSource = app.get<DataSource>(getDataSourceToken());
    Utils.appRepo = dataSource.getRepository(App);
    Utils.appTranslationRepo = dataSource.getRepository(AppTranslation);
    Utils.userRepo = dataSource.getRepository(User);
    Utils.activationRepo = dataSource.getRepository(Activation);
    Utils.logger = app.get<SALogger>(SALogger);
    await Utils.cleanDb();

    await bootstrap(app);
    Utils.app = app;
    return Utils.app;
  }

  static async stopTestServer(app: INestApplication) {
    await app?.close();
  }

  static assertApiBody(body: Response['body'], expected: unknown) {
    expect(body).toEqual(expected);
  }

  private static getEntities() {
    const dataSource = Utils.app.get<DataSource>(getDataSourceToken());
    return dataSource.entityMetadatas.map((e) => ({ name: e.name, tableName: e.tableName }));
  }

  static async cleanDb() {
    if (!Utils.app) {
      return;
    }
    try {
      const entities = Utils.getEntities();
      const dataSource = Utils.app.get<DataSource>(getDataSourceToken());
      for (const entity of entities) {
        await dataSource.query(`TRUNCATE TABLE "${entity.tableName}" CASCADE;`);
      }
      // Leave user and app sequences as they used to be after migration to avoid some issues with
      // fixtures
      await dataSource.query(`ALTER SEQUENCE "user_id_seq" RESTART WITH 1000000000`);
      await dataSource.query(`ALTER SEQUENCE "app_id_seq" RESTART WITH 1000000000`);
    } catch (error) {
      throw new Error(`ERROR: Cleaning test db: ${error}`);
    }
  }

  static async getAppOAuthAppToken(appId: number) {
    const dataSource = Utils.app.get<DataSource>(getDataSourceToken());
    const app = await dataSource.getRepository(App).findOneByOrFail({ id: appId });
    const jwkService = Utils.app.get<JWKService>(JWKService);
    return jwkService.sign(
      {
        clientId: app.oauthClientId,
        appId: app.id,
        scope: EOAuthScope.APP,
      },
      app.orgEnvId,
    );
  }

  static async getAppOAuthUserToken(appId: number, userId: number) {
    const dataSource = Utils.app.get<DataSource>(getDataSourceToken());
    const app = await dataSource.getRepository(App).findOneByOrFail({ id: appId });
    const jwkService = Utils.app.get<JWKService>(JWKService);
    return jwkService.sign(
      {
        clientId: app.oauthClientId,
        appId: app.id,
        scope: EOAuthScope.USER,
        userId,
        appPermissions: ['chat.voice'],
        isMinor: true,
      } satisfies JwtUserPayload,
      app.orgEnvId,
    );
  }

  static async getMobileAppOAuthToken(appId: number) {
    const dataSource = Utils.app.get<DataSource>(getDataSourceToken());
    const app = await dataSource.getRepository(App).findOneByOrFail({ id: appId });
    const jwkService = Utils.app.get<JWKService>(JWKService);
    return jwkService.sign(
      {
        clientId: app.oauthClientId,
        appId: app.id,
        scope: EOAuthScope.MOBILE_APP,
      },
      app.orgEnvId,
    );
  }

  static mockAgeGateAPI(mockAgeGateResponse?: AgeGateDTO): nock.Scope {
    const { baseURL } = this.configService.getAgeGateService();

    return nock(baseURL)
      .persist()
      .get(/v1\/age/)
      .reply(200, (uri: string) => {
        const url = new URL(`${baseURL}${uri}`);

        return {
          response: mockAgeGateResponse || {
            country: url.searchParams.get('country') || ageGateResponse.country,
            consentAge: 13,
            userAge: url.searchParams.has('dob')
              ? differenceInYears(new Date('2024-09-09'), new Date(url.searchParams.get('dob') + ''))
              : undefined,
            underAgeOfDigitalConsent: url.searchParams.has('dob')
              ? differenceInYears(new Date('2024-09-09'), new Date(url.searchParams.get('dob') + '')) < 13
              : undefined,
          },
          meta: { requestId: randomUUID(), timestamp: new Date().toISOString() },
        };
      });
  }

  static mockDevPortalAPI(mockWebhookSecretResponse?: ServiceWebhookListDTO): nock.Scope {
    const { baseURL } = this.configService.getDevPortalService();

    return nock(baseURL)
      .persist()
      .get(/v1\/developers\/orgs\/.*\/org-environments\/.*\/services\/.*\/webhooks/)
      .reply(200, () => {
        return {
          response: mockWebhookSecretResponse,
          meta: { requestId: randomUUID(), timestamp: new Date().toISOString() },
        };
      });
  }

  static mockCallbackServiceWebhook(
    webhookName: EWebhookName,
    environmentId: string,
    serviceId: string,
    secret: string,
  ) {
    const { baseURL } = this.configService.getCallbackApiClient();

    return nock(baseURL)
      .get(`/v1/callbacks/webhooks/${webhookName}/environments/${environmentId}/services/${serviceId}`)
      .reply(200, () => {
        return {
          response: {
            environmentId: environmentId,
            serviceId: serviceId as TServiceID,
            name: webhookName,
            secrets: [
              {
                value: secret,
                version: 0,
              },
            ],
            url: 'someUrl',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          } satisfies WebhookDTO,
        };
      });
  }

  static mockFamilyAdminGroups() {
    const { baseURL } = Utils.configService.getFamilyService();
    nock(baseURL)
      .persist()
      .get(/v1\/families\/admin\/groups/)
      .query(true)
      .reply(200, {
        response: {
          familyGroups: [
            {
              id: '00000000-0000-0000-0000-000000000000',
              members: [
                {
                  id: '00000000-0000-0000-0000-000000000001',
                  role: 'manager',
                  email: '<EMAIL>',
                },
              ],
              allowedToAddManagers: false,
              allowedToAddSupervisedUnsupervised: false,
              allowedToAddSupervisors: false,
            },
          ],
        },
        meta: { requestId: 'test-request-id', timestamp: new Date().toISOString() },
      });
  }

  static mockSettingsBackendAPI(): nock.Scope {
    const { baseURL } = this.configService.getSettingsService();

    return nock(baseURL)
      .persist()
      .post(/send-consent-email$/g, (body: { [k: string]: unknown }) => !body.parentEmail)
      .reply(400, { freekwsErrorCode: 'consent_request_missing_parent_email' })
      .post(/send-consent-email$/g, (body: { [k: string]: unknown }) => !!body.parentEmail)
      .reply(200, {
        response: {
          consents: [
            {
              consentId: randomUUID(),
              consentName: 'test-consent',
            },
          ],
          settings: [
            {
              namespace: 'chat',
              settingName: 'voice',
              effectiveValue: true,
            },
          ],
        },
      })
      .get(/values/g)
      .reply(200, {
        response: {
          settings: [voiceSetting] as UserSettingValueShortDTO[],
        },
      })
        .get(/v1\/settings\/users\/.*\/products\/.*\/values/g)
        .reply(200, {
          response: {
            settings: [],
          },
          meta: { requestId: 'test-request-id', timestamp: new Date().toISOString() },
        })
      .delete(/v1\/settings\/admin\/users\/.*\/values/g)
      .reply(200)
      .post(/v1\/settings\/admin\/users\/.*\/products\/.*\/send-consent-email/g)
      .reply(200, {
        response: {
          settings: [voiceSetting],
        },
      })
      .post(/v1\/settings\/admin\/users\/.*\/products\/.*\/generate-consent-request/g)
        .reply(200)
        .get(/v1\/settings\/internal-admin\/orgs\/.*\/org-envs\/.*\/products\/.*\/product-envs\/.*\/definitions/g)
        .reply(200, {
          response: {
            definitions: [
              `version: 1
orgId: "6c039d20-8a03-45de-ba5e-54b3ef581d85"
productId: "7a6002f7-12dd-471a-88f2-da8f5317645d"
namespace: "chat"
settings:
  - settingName: "voice"
    valueType: "boolean"
    userHidden: false
    required: false
    autoReviewConsent: false
    irrevocable: false
    label:
      en: "Voice chat"
    parentNotice:
      en: "Allow user to use voice chat"
    userNotice:
      en: "Voice chat permission"
    regions: []`
            ]
          },
          meta: { requestId: 'test-request-id', timestamp: new Date().toISOString() },
        });
  }

  static mockSettingsSendConsentEmail() {
    const settingsBaseUrl = Utils.configService.getSettingsService().baseURL;
    return nock(settingsBaseUrl)
      .post(/\/v1\/settings\/admin\/users\/.*\/products\/.*\/send-consent-email/)
      .reply(200, {
        response: {
          settings: [
            {
              namespace: 'chat',
              settingName: 'voice',
              value: true,
              valueType: 'boolean',
            },
          ],
        },
      });
  }

  static mockDeleteUserSettings() {
    const { baseURL } = this.configService.getSettingsService();
    return nock(baseURL)
      .post(/\/v1\/settings\/users\/.*\/products\/.*\/values\/delete/g)
      .reply(200);
  }

  static mockGetUserSettingsWithVoiceSetting() {
    const { baseURL } = this.configService.getSettingsService();
    const voiceSetting = {
      namespace: 'chat',
      settingName: 'voice',
      preferredValue: true,
      effectiveValue: true,
      consentRequestedAt: Date.now() - 1000,
      definition: {
        orgId: '6c039d20-8a03-45de-ba5e-54b3ef581d85',
        namespace: 'chat',
        settingName: 'voice',
        valueType: 'boolean',
        translations: {
          en: {
            label: 'Voice chat',
            parentNotice: 'Allow user to use voice chat',
          },
        },
        ageBracket: {
          consentType: 'opt-in-verified',
        },
      },
    };
    const scope = nock(baseURL)
      .get(/\/v1\/settings\/users\/.*\/products\/.*\/values/)
      .reply(200, {
        response: {
          settings: [voiceSetting],
        },
        meta: { requestId: randomUUID(), timestamp: new Date().toISOString() },
      });
    return { scope, voiceSetting };
  }

  static mockFamilyServiceAPI(times = 1, verifiedUserId?: number): nock.Scope {
    const { baseURL } = this.configService.getFamilyService();

    return nock(baseURL)
      .get(/v1\/families\/admin\/groups/g)
      .times(times)
      .reply(200, (uri: string) => {
        const members =
          new URL(`${baseURL}${uri}`).searchParams.get('userId') === `${verifiedUserId}`
            ? [
                {
                  id: '00000000-0000-0000-0000-000000000001',
                  role: 'manager',
                  email: '<EMAIL>',
                },
              ]
            : [];
        return {
          response: {
            familyGroups: [
              {
                id: '00000000-0000-0000-0000-000000000000',
                members,
                allowedToAddManagers: false,
                allowedToAddSupervisedUnsupervised: false,
                allowedToAddSupervisors: false,
              },
            ],
          } as FamilyGroupListDTO,
          meta: { requestId: randomUUID(), timestamp: new Date().toISOString() },
        };
      });
  }

  static mockFamilyServiceGuardianRequest(email?: string): nock.Scope {
    const { baseURL } = this.configService.getFamilyService();

    const mockEmail = email;

    return nock(baseURL)
      .get(/v1\/families\/internal-admin\/guardian-links/g)
      .query(true)
      .reply(200, () => {
        let confirmedGuardianDto: ConfirmedGuardianLinkDTO[] = [];
        if (mockEmail) {
          confirmedGuardianDto = [
            {
              guardian: {
                email: mockEmail,
              } as unknown as ConfirmedGuardianLinkDTO['guardian'],
              orgEnvId: 'test-org-env-id',
              familyGroupId: 'test-family-group-id',
              familyCreatedAt: 'someDate',
              user: {} as unknown as ConfirmedGuardianLinkDTO['user'],
            },
          ];
        }
        return {
          response: {
            pendingRequests: [],
            confirmedRequests: confirmedGuardianDto,
          } as GuardianLinkListDTO,
          meta: { requestId: randomUUID(), timestamp: new Date().toISOString() },
        };
      });
  }

  static mockGetUserFamily(): nock.Scope {
    const { baseURL } = this.configService.getFamilyService();
    const members = [
      {
        id: '00000000-0000-0000-0000-000000000001',
        role: 'manager',
        email: '<EMAIL>',
      },
    ];

    return nock(baseURL)
      .persist()
      .get(/v1\/families\/admin\/groups/)
      .reply(200, () => {
        return {
          response: {
            familyGroups: [
              {
                id: '00000000-0000-0000-0000-000000000003',
                members,
                allowedToAddManagers: false,
                allowedToAddSupervisedUnsupervised: false,
                allowedToAddSupervisors: false,
              },
            ],
          } as FamilyGroupListDTO,
          meta: { requestId: randomUUID(), timestamp: new Date().toISOString() },
        };
      });
  }

  static mockKeycloakTokenSettingResponse(returnValue: string = 'mocked-token'): nock.Scope {
    return nock('http://keycloak:8080', { allowUnmocked: true })
        .persist()
        .post(
            '/auth/realms/kws/protocol/openid-connect/token',
            (body) => {
              return body.grant_type === 'client_credentials' && body.scope === "settings";
            }
        )

        .reply(200, returnValue, {
          'Content-Type': 'text/plain',
        })
  }

  static mockPreverificationServiceAPI(): nock.Scope {
    const { baseURL } = this.configService.getPreVerificationService();

    return nock(baseURL)
      .persist()
      .get(/v1\/pre(?:verifications\/){2}/)
      .reply((uri: string) => {
        const url = new URL(`${baseURL}${uri}`);
        const email = Utils.encryptionService.decrypt(decodeURIComponent(url.pathname.split('/').at(-1) || ''));

        if (email === '<EMAIL>') {
          return [
            200,
            {
              response: {
                emailHash: Utils.hashingService.hashURIEncodedEmail(email),
                verifications: [
                  {
                    emailHash: Utils.hashingService.hashURIEncodedEmail(email),
                    verificationMethod: 'stripe',
                    userContext: 'parent',
                    verificationDate: new Date('2024-10-10'),
                    createdAt: new Date('2024-10-10'),
                    expiresAt: new Date('2026-10-10'),
                    userLocation: 'us',
                    verificationType: 'payment-card',
                    verificationStandard: 'basic',
                    minimumAge: 16,
                    orgId: url.searchParams.get('orgId'),
                  },
                ],
                consents: [],
              },
              meta: { requestId: randomUUID(), timestamp: new Date().toISOString() },
            },
          ];
        }
        return [
          404,
          {
            error: {
              message: 'The requested endpoint/resource was not found',
            },
            meta: { requestId: randomUUID(), timestamp: new Date().toISOString() },
          },
        ];
      });
  }

  static mockAnalyticServiceAPI(): nock.Scope {
    const { baseURL } = this.configService.getAnalytic();

    return nock(baseURL).persist().post('/v2/events').reply(200, {});
  }

  static mockAnalyticServiceV1EventsAPI() {
    const analyticsBaseUrl = Utils.configService.getAnalytic().baseURL;
    return nock(analyticsBaseUrl)
      .post('/v1/events')
      .reply(200, {
        response: { success: true },
      });
  }

  static async loadFixtures(): Promise<TFixture> {
    const dataSource = Utils.app.get<DataSource>(getDataSourceToken());

    const result: TFixture = {};
    const connection = dataSource.manager.connection;
    const loader = new Loader();
    loader.load(__dirname + '/../fixtures');

    const resolver = new Resolver();
    const dbFixtures = resolver.resolve(loader.fixtureConfigs);
    const builder = new Builder(connection, new Parser(), false);

    for (const fixture of fixturesIterator(dbFixtures)) {
      const entity = await builder.build(fixture);
      const savedEntity = await dataSource.manager.getRepository(entity.constructor.name).save(entity);
      const entityName = entity.constructor.name;
      result[entityName] = result[entityName] || [];
      result[entityName].push(savedEntity);
    }

    this.fixtures = result;

    return this.fixtures;
  }

  static generateKwsSignature(timestamp: number, body: OrgLevelWebhookPayload, secretKey: string): string {
    const bodyString = JSON.stringify(body);
    const dataToSign = `${timestamp}.${bodyString}`;
    return createHmac('sha256', secretKey).update(dataToSign).digest('hex');
  }

  static async createUserActivation(userId: number, appId: number) {
    const user = await this.userRepo.findOneOrFail({ where: { id: userId } });
    const app = await this.appRepo.findOneOrFail({ where: { id: appId } });

    return await this.activationRepo.save({
      app: app,
      user: user,
    });
  }

  static async createAppTranslations(appId: number, orgEnvId: string, translations: Partial<AppTranslation>[]) {
    const translationsToSave = translations.map((translation) => ({
      appId,
      orgEnvId,
      ...translation,
    }));

    return await this.appTranslationRepo.save(translationsToSave);
  }

  static async cleanupAppTranslations(appId: number, orgEnvId: string) {
    await this.appTranslationRepo.delete({ appId, orgEnvId });
  }

  static async getKeycloakTestUtils(): Promise<KeycloakTestUtils> {
    if (!this.keycloakTestUtils) {
      this.logger.info('Creating a new keycloak client');
      const configService = new ConfigService();
      const { authServerUrl } = configService.getKeycloak();
      this.keycloakTestUtils = new KeycloakTestUtils({ authServerUrl, realm: 'kws' });
      await this.keycloakTestUtils.connect();
    }
    return this.keycloakTestUtils;
  }

  static async getServiceAccessToken(clientId: string, clientSecret: string, optionalScope?: string): Promise<string> {
    if (!this.keycloakTestUtils) {
      await Utils.getKeycloakTestUtils();
    }

    const body: { grant_type: string; scope?: string } = {
      grant_type: 'client_credentials',
    };
    if (optionalScope) {
      body.scope = optionalScope;
    }

    try {
      const { access_token } = await this.keycloakTestUtils.makeRequest<TTokenResponse>(
        `/realms/kws/protocol/openid-connect/token`,
        '/realms/:realm/protocol/openid-connect/token',
        QueryString.stringify(body),
        clientId,
        clientSecret,
      );
      return access_token;
    } catch (error) {
      Utils.logger.error('GET_SERVICE_TOKEN_ERROR', { error });
      throw new Error('GET_TOKEN_ERROR', { cause: error });
    }
  }

  static buildFullResponse<T>(response: T) {
    return {
      data: this.buildResponse<T>(response),
      status: 200,
    };
  }

  static buildResponse<T>(response: T) {
    return {
      response,
      meta: {
        requestId: 'testId',
        timestamp: 'testStamp',
      },
    };
  }

  static axiosErrorFactory(status: number, responseData?: AxiosErrorResponseData | undefined): AxiosError {
    const message = responseData ? responseData?.error?.message : 'test-error';
    const error = new Error(message);
    (error as AxiosError).response = {
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      config: undefined!,
      data: responseData,
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      headers: undefined!,
      statusText: '',
      status,
    };
    return error as AxiosError;
  }
}

export type AxiosErrorResponseData = {
  error: {
    statusCode?: number;
    message?: string;
  };
};

export async function createUser(app: INestApplication, JWT_APP_TOKEN: string, appId: number, host: string) {
  const { body: unverifiedBody } = await makeRequest(app, 'post', `/v2/apps/${appId}/users`, {
    headers: {
      Authorization: `Bearer ${JWT_APP_TOKEN}`,
      'x-forwarded-host': host,
    },
    body: {
      country: 'US',
      dateOfBirth: '2014-10-10',
      parentEmail: '<EMAIL>',
      language: 'en',
      permissions: ['chat.voice'],
    },
    expectedStatus: 201,
  });

  expect(unverifiedBody.id).toBeDefined();

  return unverifiedBody.id as number;
}
