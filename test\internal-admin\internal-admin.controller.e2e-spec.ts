import { INestApplication } from '@nestjs/common';
import { UpdateUserDateOfBirthDTO } from '@superawesome/freekws-classic-wrapper-common';
import { EServiceClient, serviceClients } from '@superawesome/freekws-test-keycloak';
import request from 'supertest';

import { App } from '../../src/app/app.entity';
import { Utils } from '../utils';
import { ORG_ENV_ID } from '../utils/constants';
import { makeRequest } from '../utils/request-helper';

describe('InternalAdminController (e2e)', () => {
  let app: INestApplication;
  let JWT_APP_TOKEN: string;
  let testApp: App;
  let USER_ID: number;

  beforeAll(async () => {
    app = await Utils.createTestServer();

    Utils.mockAgeGateAPI();
    Utils.mockSettingsBackendAPI();
    Utils.mockAnalyticServiceAPI();
    Utils.mockPreverificationServiceAPI();
    Utils.mockDevPortalAPI();
    Utils.mockKeycloakTokenSettingResponse()

    await Utils.cleanDb();
    const fixtures = await Utils.loadFixtures();
    testApp = fixtures.App.find((app) => app.name === 'test-app') as App;
  });

  beforeEach(async () => {
    JWT_APP_TOKEN = await Utils.getAppOAuthAppToken(testApp.id);
    const { body } = await makeRequest(app, 'post', `/v2/apps/${testApp.id}/users`, {
      headers: {
        Authorization: `Bearer ${JWT_APP_TOKEN}`,
        'x-forwarded-host': testApp.orgEnv.host,
      },
      body: {
        country: 'US',
        dateOfBirth: '2014-10-10',
        parentEmail: '<EMAIL>',
        language: 'en',
        permissions: ['chat.voice'],
        username: 'testuser',
      },
      expectedStatus: 201,
    });
    USER_ID = body.id;
  });

  afterAll(async () => {
    await Utils.stopTestServer(app);
    jest.clearAllMocks();
  });

  describe('PUT /internal-admin/users/:userId/dob', () => {
    it('should get 401 for invalid user', async () => {
      const userId = 2324;
      const body: UpdateUserDateOfBirthDTO = {
        dateOfBirth: '2011-10-10',
        location: 'US',
      };
      await request(app.getHttpServer())
        .put(`/internal-admin/org-envs/${testApp.orgEnv.id}/users/${userId}/dob`)
        .send(body)
        .expect(401);
    });

    it('should get 200', async () => {
      const apiClient = serviceClients.find((c) => c.clientId === EServiceClient.DEVPORTAL);
      if (!apiClient) {
        expect(apiClient).toBeDefined();
        return;
      }

      const token = await Utils.getServiceAccessToken(apiClient.clientId, apiClient.clientSecret);

      const body: UpdateUserDateOfBirthDTO = {
        dateOfBirth: '2011-10-10',
        location: 'US',
      };
      await request(app.getHttpServer())
        .put(`/internal-admin/org-envs/${testApp.orgEnv.id}/users/${USER_ID}/dob`)
        .set({ authorization: `Bearer ${token}` })
        .send(body)
        .expect(200);
    });
  });

  describe('POST /internal-admin/org-envs/:orgEnvId/users/:userId/delete-account', () => {
    it('deletes a user account', async () => {
      const userRepo = app.get('UserRepository');
      const usersBefore = await userRepo.find({ where: { id: USER_ID } });
      expect(usersBefore.length).toBe(1);

      const apiClient = serviceClients.find((c) => c.clientId === EServiceClient.DEVPORTAL);
      if (!apiClient) {
        expect(apiClient).toBeDefined();
        return;
      }

      const token = await Utils.getServiceAccessToken(apiClient.clientId, apiClient.clientSecret);

      await request(app.getHttpServer())
        .post(`/internal-admin/org-envs/${testApp.orgEnv.id}/users/${USER_ID}/delete-account`)
        .set({ authorization: `Bearer ${token}` })
        .send({})
        .expect(200);

      const usersAfter = await userRepo.find({ where: { id: USER_ID } });
      expect(usersAfter.length).toBe(0);
    });

    it('responds with bad request when password is provided', async () => {
      const userRepo = app.get('UserRepository');
      const usersBefore = await userRepo.find({ where: { id: USER_ID } });
      expect(usersBefore.length).toBe(1);

      const apiClient = serviceClients.find((c) => c.clientId === EServiceClient.DEVPORTAL);
      if (!apiClient) {
        expect(apiClient).toBeDefined();
        return;
      }

      const token = await Utils.getServiceAccessToken(apiClient.clientId, apiClient.clientSecret);

      await request(app.getHttpServer())
        .post(`/internal-admin/org-envs/${testApp.orgEnv.id}/users/${USER_ID}/delete-account`)
        .set({ authorization: `Bearer ${token}` })
        .send({
          password: 'farores-wind',
        })
        .expect(400);

      const usersAfter = await userRepo.find({ where: { id: USER_ID } });
      expect(usersAfter.length).toBe(1);
    });
  });

  describe('GET /internal-admin/users', () => {
    it('should return 400 if username is less than 3 characters', async () => {
      const apiClient = serviceClients.find((c) => c.clientId === EServiceClient.DEVPORTAL);
      if (!apiClient) {
        expect(apiClient).toBeDefined();
        return;
      }

      const token = await Utils.getServiceAccessToken(apiClient.clientId, apiClient.clientSecret);

      await request(app.getHttpServer())
        .get('/internal-admin/users?username=te')
        .set({ authorization: `Bearer ${token}` })
        .expect(400);
    });

    it('should return 401 if not authenticated with keycloak token', async () => {
      await request(app.getHttpServer())
        .get('/internal-admin/users?username=test')
        .set({ authorization: `Bearer ${JWT_APP_TOKEN}` })
        .expect(401);
    });

    it('should return 403 if not authenticated with devportal-api scope', async () => {
      const apiClient = serviceClients.find((c) => c.clientId === EServiceClient.PARENT_PORTAL);
      if (!apiClient) {
        expect(apiClient).toBeDefined();
        return;
      }

      const token = await Utils.getServiceAccessToken(apiClient.clientId, apiClient.clientSecret);

      await request(app.getHttpServer())
        .get('/internal-admin/users?username=test')
        .set({ authorization: `Bearer ${token}` })
        .expect(403);
    });

    it('should return matching users with parent emails', async () => {
      await Utils.userRepo.save([
        {
          dateOfBirth: "2014-10-10'",
          language: 'en',
          username: 'testuserForSearch',
          orgEnvId: ORG_ENV_ID,
        },
        {
          dateOfBirth: "2014-10-10'",
          language: 'en',
          username: 'testuserForMoreSearching',
          orgEnvId: ORG_ENV_ID,
        },
      ]);

      Utils.mockFamilyServiceGuardianRequest('<EMAIL>');
      Utils.mockFamilyServiceGuardianRequest('<EMAIL>');

      const apiClient = serviceClients.find((c) => c.clientId === EServiceClient.DEVPORTAL);
      if (!apiClient) {
        expect(apiClient).toBeDefined();
        return;
      }

      const token = await Utils.getServiceAccessToken(apiClient.clientId, apiClient.clientSecret);

      const { body } = await request(app.getHttpServer())
        .get('/internal-admin/users?username=testuserFor')
        .set({ authorization: `Bearer ${token}` })
        .expect(200);

      expect(Array.isArray(body)).toBe(true);
      expect(body.length).toBe(2);
      expect(body).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            username: 'testuserForSearch',
            parentEmail: '<EMAIL>',
          }),
          expect.objectContaining({
            username: 'testuserForMoreSearching',
            parentEmail: '<EMAIL>',
          }),
        ]),
      );
    });
  });
});
